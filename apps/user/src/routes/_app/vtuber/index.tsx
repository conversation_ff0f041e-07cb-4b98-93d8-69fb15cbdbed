import { createFileRoute, notFound, useNavigate } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import {
  vtuberCategoryClient,
  vtuberProfilesClient,
} from "@vtuber/services/client";
import { But<PERSON> } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { ContentHeading } from "@vtuber/ui/components/content-heading";
import { ScrollArea, ScrollBar } from "@vtuber/ui/components/scroll-area";
import { useIsMobile } from "@vtuber/ui/hooks/use-mobile";
import { truncateString } from "@vtuber/ui/lib/truncate-string";
import { z } from "zod";
import { CategorySearch } from "~/components/category-search";
import { CallToAction } from "~/components/layout/call-to-action";
import { KeywordSearch } from "~/components/layout/keyword-search";
import { PageTitle } from "~/components/layout/page-title";
import { VtuberList } from "~/components/vtuber/vtuber-list";
import { seo } from "~/utils/seo";

export const Route = createFileRoute("/_app/vtuber/")({
  component: RouteComponent,
  validateSearch: z.object({
    catId: z.string().optional(),
  }),
  loader: async () => {
    const [vtuber, err] = await vtuberProfilesClient.getAllVtuberProfiles({
      pagination: {
        size: 20,
      },
    });

    const [categories] = await vtuberCategoryClient.getAllVtuberCategories({});

    if (err) {
      throw notFound({
        data: err.rawMessage,
      });
    }

    return { vtuber, categories };
  },
  staleTime: 60000,
  head: () => ({
    meta: seo({
      title: "V-SAI | Vtubers",
      description: "Vtubers on V-SAI.",
      url: "/vtuber",
      keywords:
        "VTuber, V-SAI, V-SAI - meet your favorite VTubers, vtubers, VTuber vtubers",
    }),
  }),
});

function RouteComponent() {
  const { vtuber, categories } = Route.useLoaderData();
  const navigate = useNavigate({ from: Route.fullPath });
  const { catId } = Route.useSearch();
  const { getText } = useLanguage();
  const { isMobile } = useIsMobile();

  const vtuberCategories = categories?.categories || [];

  return (
    <div className="relative pt-20">
      <div className="grid gap-y-28">
        <Container className="grid gap-y-16">
          <PageTitle title={"Vtuber"} />
          <div className="grid sm:gap-y-16 gap-y-12">
            {vtuberCategories.length > 0 ? (
              <ScrollArea className="overflow-hidden">
                <section className="flex items-center md:flex-wrap flex-nowrap gap-4 justify-center">
                  <Button
                    className="rounded-full"
                    onClick={() => {
                      navigate({
                        search: {
                          catId: undefined,
                        },
                      });
                    }}
                    variant={!catId ? "default" : "outline"}>
                    {getText("all")}
                  </Button>
                  {categories?.categories.map((c) => (
                    <Button
                      className="rounded-full"
                      onClick={() => {
                        navigate({
                          search: {
                            catId: c?.id.toString(),
                          },
                        });
                      }}
                      variant={catId === c?.id ? "default" : "outline"}
                      key={c?.id.toString()}>
                      {isMobile
                        ? `${truncateString(c?.name!, 10)}...`
                        : c?.name}
                    </Button>
                  ))}
                </section>

                <ScrollBar orientation="horizontal" />
              </ScrollArea>
            ) : null}
            <VtuberList
              initialData={vtuber}
              catId={catId}
            />
          </div>
          <section className="space-y-12 sm:pt-24 pt-8">
            <ContentHeading
              title="キーワード/カテゴリ検索"
              subTitle="Keyword Category Search"
              className="text-balance"
            />
            <div className="sm:space-y-12 space-y-20">
              <KeywordSearch />
              <CategorySearch />
            </div>
          </section>
        </Container>

        <CallToAction />
      </div>
    </div>
  );
}
