import { createFileRoute, <PERSON>, useNavigate } from "@tanstack/react-router";
import { Button } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { Form } from "@vtuber/ui/components/form";
import { SelectInput } from "@vtuber/ui/components/form-inputs/select-input";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { InputBadge } from "@vtuber/ui/components/input-badge";
import { ArrowRight, Triangle } from "lucide-react";
import { useForm } from "react-hook-form";
import { PageTitle } from "~/components/layout/page-title";

export const Route = createFileRoute("/_app/campaign/$id/contact/")({
  component: RouteComponent,
});
function RouteComponent() {
  const navigate = useNavigate();
  const { id } = Route.useParams();
  const form = useForm({
    defaultValues: {
      companyName: "",
      name: "",
      projectUrl: "",
      inquiryType: "",
      email: "",
      emailConfirm: "",
      phoneNo: "",
      inquiryDetails: "",
    },
  });
  return (
    <Container className="pt-20">
      <PageTitle title="contact_page_title" />
      <div className="md:max-w-[824px] mx-auto w-full grid gap-y-[35px] text-font sm:pt-20 pt-10">
        <h3 className="sm:text-lg text-sm leading-[180%]">
          リターン内容や配送確認、プロジェクトに関するご質問は、以下のフォームよりご利用ください。ご不明な点がありましたら、お問い合わせ前に{" "}
          「
          <Link
            to="/faq"
            className="text-blue01 hover:underline">
            よくある質問
          </Link>
          」 をご確認ください。
        </h3>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit((v) => {
              console.log(v);
              navigate({
                to: "/campaign/$id/contact/thanks",
                params: { id },
              });
            })}
            className="gap-y-[72px] grid">
            <div className="grid gap-y-[35px]">
              <div className="space-y-[10px]">
                <TextInput
                  control={form.control}
                  wrapperClassName="gap-y-[10px]"
                  name="companyName"
                  className="rounded-xs h-[50px] placeholder:text-[#505050]"
                  placeholder="例) V祭 太郎"
                  label={
                    <div className="flex items-center gap-2">
                      <p className="font-bold text-font">お名前</p>
                      <InputBadge />
                    </div>
                  }
                />
              </div>
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                placeholder="音ノ癒いろは 3D新衣装プロジェクト～新たな夢のステージへ～"
                name="name"
                className="rounded-xs h-[50px] placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">プロジェクト名</p>
                    <InputBadge />
                  </div>
                }
              />
              <SelectInput
                Icon={Triangle}
                options={[
                  {
                    label: "キャンペーン内容について",
                    value: "キャンペーン内容について",
                  },
                  {
                    label: "リターン品について",
                    value: "リターン品について",
                  },
                  {
                    label: "プラン購入について",
                    value: "プラン購入について",
                  },
                  {
                    label: "その他質問",
                    value: "その他質問",
                  },
                ]}
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                placeholder="ーお問い合わせ種類を選択してください"
                name="projectUrl"
                className="rounded-xs h-[50px] placeholder:text-[#505050] [&_svg]:fill-font [&_svg]:rotate-180"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">お問い合わせ種類</p>
                    <InputBadge type="required" />
                  </div>
                }
              />
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                placeholder="例) <EMAIL>"
                name="inquiryType"
                className="rounded-xs h-[50px] placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">メールアドレス</p>
                    <InputBadge />
                  </div>
                }
              />
              <div className="space-y-[10px]">
                <TextInput
                  control={form.control}
                  wrapperClassName="gap-y-[10px]"
                  name="email"
                  type="email"
                  inputMode="email"
                  className="rounded-xs h-[50px] placeholder:text-[#505050]"
                  placeholder="例) <EMAIL>"
                  label={
                    <div className="flex items-center gap-2">
                      <p className="font-bold text-font">
                        確認用メールアドレス
                      </p>
                      <InputBadge />
                    </div>
                  }
                />
                <ul className="list-disc pl-5 text-sm space-y-[18px]">
                  <li>入力したメールアドレスへのみ返信いたします。</li>
                  <li>
                    携帯キャリア（au、softbank、docomo）やicloudメール、outlookメールの場合、初期設定で迷惑メールフィルタが設定されており、メールが届かないことがあります。受信設定をご確認の上、お問い合わせください。
                  </li>
                  <li>
                    返信にお時間をいただく場合がありますので、予めご了承ください。
                  </li>
                </ul>
              </div>
              <TextInput
                control={form.control}
                wrapperClassName="gap-y-[10px]"
                name="emailConfirm"
                type="email"
                inputMode="email"
                className="rounded-xs h-[50px] placeholder:text-[#505050]"
                placeholder="例) <EMAIL>"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">確認用メールアドレス</p>
                    <InputBadge />
                  </div>
                }
              />
              <div className="space-y-[10px]">
                <TextInput
                  control={form.control}
                  wrapperClassName="gap-y-[10px]"
                  name="phoneNo"
                  type="tel"
                  inputMode="tel"
                  className="rounded-xs h-[50px] placeholder:text-[#505050]"
                  placeholder="例) 090-1234-5678"
                  label={
                    <div className="flex items-center gap-2">
                      <p className="font-bold text-font">電話番号</p>
                      <InputBadge type="optional" />
                    </div>
                  }
                />
                <ul className="list-inside list-disc text-sm text-font">
                  <li>ハイフンあり でご入力ください。</li>
                </ul>
              </div>
              <TextAreaInput
                minHeight={215}
                className="rounded-xs placeholder:text-[#505050]"
                label={
                  <div className="flex items-center gap-2">
                    <p className="font-bold text-font">お問い合わせ内容</p>
                    <InputBadge />
                  </div>
                }
                control={form.control}
                name="inquiryDetails"
                placeholder="お問い合わせ内容を詳しくご記載ください。"
              />
            </div>
            <div className="bg-[#2C2820] sm:p-8 p-4 rounded-xs leading-[180%]">
              <ul className="list-disc space-y-[8px] text-[#CCCCCC] font-medium pl-5">
                <li>不適切な言葉は、利用制限や強制退会の対象となります。</li>
                <li>
                  重複した内容がないか、事前に「
                  <Link
                    to="/faq"
                    className="text-blue01 hover:underline">
                    よくある質問
                  </Link>
                  」のご確認をお願いいたします。
                </li>
              </ul>
            </div>
            <Button
              variant={"tertiary"}
              className="h-[68px] base:w-[336px] w-full mx-auto text-white block pl-[46px] pr-[30px]">
              <div className=" flex justify-between items-center w-full">
                <p className="font-bold text-lg">送信する</p>
                <ArrowRight />
              </div>
            </Button>
          </form>
        </Form>
      </div>
    </Container>
  );
}
