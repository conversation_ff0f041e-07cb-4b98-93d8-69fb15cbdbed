import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { buttonVariants } from "@vtuber/ui/components/button";
import { cn } from "@vtuber/ui/lib/utils";
import { CallToAction } from "~/components/layout/call-to-action";

export const Route = createFileRoute("/_app/campaign/$id/contact/thanks")({
  component: RouteComponent,
});
function RouteComponent() {
  const { getText } = useLanguage();
  return (
    <div>
      <div className="text-font sm:max-w-[824px] mx-auto sm:px-0 px-5 flex flex-col items-center py-40 gap-y-16">
        <h1 className="text-center font-bold sm:text-[40px] text-2xl w-fit leading-relaxed mx-auto bg-clip-text text-transparent bg-gradient-text">
          プロジェクトオーナーへお問い合わせいただき ありがとうございます。
        </h1>
        <section className="gap-y-[56px] flex flex-col items-center">
          <div className="sm:text-lg space-y-10">
            <p>
              プロジェクトオーナーのお問い合わせを受け付けました。お問い合わせ内容を確認させていただき、メールにて回答いたします。送信内容は、確認のためご入力のメールアドレスへ送信しております。
            </p>
            <p>
              万が一、メールが届いていない場合は、メールアドレスの記入ミスや、迷惑メール等の受信設定されている可能性があるため、再度お問い合わせください。
            </p>
          </div>
          <Link
            to="/"
            className={cn(
              buttonVariants({
                variant: "outline",
              }),
              "font-bold rounded-full h-[60px] sm:w-[424px] w-full mx-auto",
            )}>
            {getText("back_to_top")}
          </Link>
        </section>
      </div>
      <CallToAction />
    </div>
  );
}
