import { useNavigate, useParams } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import { Input } from "@vtuber/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@vtuber/ui/components/select";
import { motion } from "motion/react";
import { eachYearOfInterval, endOfYear, startOfYear } from "date-fns";
import { Search, SortAscIcon } from "lucide-react";
import { useState } from "react";

export const VtuberPostFitlers = () => {
  const { id } = useParams({ from: "/_app/vtuber/$id/" });
  const { getText } = useLanguage();
  const navigate = useNavigate();
  const [showSearch, setShowSearch] = useState(false);
  const startDate = new Date(new Date().getFullYear() - 20, 6);
  const endDate = new Date();
  const years = eachYearOfInterval({
    start: startOfYear(startDate),
    end: endOfYear(endDate),
  });
  return (
    <div className="space-y-3">
      <div className="flex flex-wrap sm:gap-y-0 gap-y-3 items-center justify-between gap-x-4 bg-background relative z-10">
        <Select>
          <SelectTrigger className="h-11 data-[placeholder]:text-white [&>svg]:text-white [&>svg]:opacity-100 w-auto flex-1">
            <SelectValue placeholder={getText("sort_by")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全て</SelectItem>
            <SelectItem value="newest">最新</SelectItem>
            <SelectItem value="oldest">古い</SelectItem>
          </SelectContent>
        </Select>
        <Select>
          <SelectTrigger className="h-11 data-[placeholder]:text-white [&>svg]:text-white [&>svg]:opacity-100 w-auto flex-1">
            <SelectValue placeholder={getText("member_rank")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="first">1位</SelectItem>
            <SelectItem value="second">2位</SelectItem>
            <SelectItem value="third">3位</SelectItem>
          </SelectContent>
        </Select>
        <Select>
          <SelectTrigger className="h-11 data-[placeholder]:text-white [&>svg]:text-white [&>svg]:opacity-100 w-auto flex-1">
            <SelectValue placeholder={getText("by_year")} />
          </SelectTrigger>
          <SelectContent>
            {years.map((y) => (
              <SelectItem
                value={new Date(y).getFullYear().toString()}
                key={y.toISOString()}>
                {new Date(y).getFullYear()}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <div className="flex items-center gap-x-3 justify-end ml-auto">
          <Button
            className="size-11"
            variant={"white-outline"}>
            <SortAscIcon />
          </Button>
          <Button
            variant={"white-outline"}
            onClick={() => {
              setShowSearch((prev) => !prev);
            }}
            className="h-11 bg-white text-sub">
            <Search className="mr-2" />
            {getText("search_post")}
          </Button>
        </div>
      </div>
      <Input
        onKeyDownCapture={(e) => {
          const key = e.key;
          if (key === "Enter") {
            navigate({
              to: "/vtuber/$id",
              params: {
                id,
              },
              search: {
                query: "search",
              },
              resetScroll: false,
            });
          }
        }}
        autoFocus
        inputMode="search"
        type="search"
        className="h-12"
        placeholder={getText("search_post") + "..."}
      />
    </div>
  );
};
