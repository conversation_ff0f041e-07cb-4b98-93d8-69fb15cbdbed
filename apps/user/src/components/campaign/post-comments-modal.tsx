import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Button } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@vtuber/ui/components/dialog";
import { Media } from "@vtuber/ui/components/media";
import { ScrollArea } from "@vtuber/ui/components/scroll-area";
import { Separator } from "@vtuber/ui/components/separator";
import { Spinner } from "@vtuber/ui/components/spinner";
import { useIsMobile } from "@vtuber/ui/hooks/use-mobile";
import { getTimeAgo } from "@vtuber/ui/lib/get-time-ago";
import { cn } from "@vtuber/ui/lib/utils";
import { Heart, MessageCircle } from "lucide-react";
import React, { useRef, useState } from "react";
import { postCommentsQueryOptions, singlePostQueryOptions } from "~/utils/api";
import { LikePost } from "../post/like-post";
import { PostCommentForm } from "../post/post-comment-form";
import { PostComments } from "../post/post-comments";

type Props = {
  postId: string;
  opened: boolean;
  setOpened: React.Dispatch<React.SetStateAction<boolean>>;
  slug: string;
};

export const PostCommentsModal = ({
  postId,
  slug,
  opened,
  setOpened,
}: Props) => {
  const [parentId, setParentId] = useState<string | undefined>(undefined);
  const [commenterName, setCommenterName] = useState<string | undefined>(
    undefined,
  );
  const { getText } = useLanguage();
  const { isMobile } = useIsMobile();
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const router = useRouter();
  const {
    isPending: loadingPost,
    data: postData,
    error,
    refetch: refetchPost,
    isRefetching: isRefetchingPost,
  } = useQuery({
    ...singlePostQueryOptions(slug),
    enabled: !!slug && opened,
  });
  const { data, isPending } = useQuery(postCommentsQueryOptions(slug));

  const comments = data?.data;

  const commentsAvailable = !isPending && comments && comments?.length > 0;
  const post = postData?.data;
  return (
    <Dialog
      open={opened}
      onOpenChange={(v) => {
        setOpened(v);
        setParentId(undefined);
        setCommenterName(undefined);
      }}>
      <DialogContent className="lg:max-w-[40%] sm:max-w-[80%] w-[90%] p-0 gap-0 gap-y-[24px]">
        <DialogHeader className="py-6 pb-0 px-[17px]">
          <DialogTitle className="sm:text-2xl text-lg">
            {isPending ? getText("loading") + "..." : post?.title}
          </DialogTitle>
        </DialogHeader>
        <Separator />

        {loadingPost || isRefetchingPost ? (
          <div className="flex items-center justify-center h-[75dvh]">
            {getText("loading")}...
          </div>
        ) : !post || error ? (
          <div className="flex items-center justify-center h-[75dvh]">
            <div className="space-y-3 text-center">
              <p className="text-2xl">
                {error ? error.message : getText("post_not_found")}
              </p>
              <Button
                onClick={() => {
                  refetchPost();
                }}
                size={"lg"}
                variant={"outline-dark"}>
                {getText("try_again")}
              </Button>
            </div>
          </div>
        ) : (
          <ScrollArea className="flex flex-col overflow-y-auto max-h-[70dvh] p-6 py-0">
            <div className="space-y-[24px]">
              <div className="flex items-center gap-3">
                <Avatar
                  className="h-14 w-14 text-xl"
                  src={post.vtuber?.image}
                  alt={post.vtuber?.name}
                  fallback={post.vtuber?.name}
                />
                <div>
                  <h3 className="text-lg font-bold">{post.vtuber?.name}</h3>
                  <DialogDescription>
                    {getTimeAgo(timestampDate(post.createdAt!))}
                  </DialogDescription>
                </div>
              </div>
              <section className="space-y-2">
                <DialogDescription className="text-foreground">
                  {post.shortDescription}
                </DialogDescription>
                {post.media && (
                  <div className="lg:h-[50dvh] sm:h-[40dvh] h-48 py-3 w-full">
                    <Media
                      style={{
                        viewTransitionName: `post-${postId}`,
                      }}
                      onClick={() => {
                        router.navigate({
                          to: "/vtuber/post/$id",
                          params: {
                            id: post.slug,
                          },
                        });
                      }}
                      className="rounded-none w-full aspect-auto"
                      src={post.media}
                      type={post.mediaType}
                      alt={post.title}
                      controls
                      showControls={!isMobile}
                    />
                  </div>
                )}
                <div className="flex items-center gap-x-[11px]">
                  <LikePost
                    userName={post.vtuber?.username}
                    postId={post.id}
                    hasLiked={post.hasLiked}
                    className="p-0 hover:bg-transparent"
                    postLikes={post.postLikes}>
                    {({ postLikes, isLiked, isLoading }) => (
                      <div className="flex items-center gap-x-[11px]">
                        {isLoading ? (
                          <Spinner className="size-[29px]" />
                        ) : (
                          <Heart
                            className={cn(
                              "size-[29px] transition-all duration-200",
                              isLiked
                                ? "text-red-500 fill-red-500 scale-110"
                                : "text-font",
                            )}
                          />
                        )}
                        <small className="text-sm font-medium text-font">
                          {postLikes}
                        </small>
                      </div>
                    )}
                  </LikePost>
                  <button
                    onClick={() => {
                      if (inputRef.current) {
                        inputRef.current.focus();
                      }
                    }}
                    className="hover:bg-transparent hover:text-white/50 flex items-center gap-x-[11px]">
                    <MessageCircle className="mr-1 size-[29px]" />
                    <p className="text-sm font-medium text-font">
                      {post.postComments}
                    </p>
                  </button>
                </div>
              </section>

              {isPending ? (
                <div className="h-40 flex justify-center items-center">
                  {getText("loading")}...
                </div>
              ) : commentsAvailable ? (
                <div className="space-y-4 mt-10 pb-6">
                  {comments?.map((comment) => {
                    return (
                      <PostComments
                        parentId={parentId}
                        postSlug={slug}
                        setParentId={setParentId}
                        setCommenterName={setCommenterName}
                        comment={comment}
                        key={comment.id}
                        creatorId={post.vtuber?.id!}
                      />
                    );
                  })}
                </div>
              ) : (
                <div className="h-40 flex justify-center items-center text-center text-gray-400 text-2xl">
                  {getText("no_comments_yet")}
                </div>
              )}
            </div>
          </ScrollArea>
        )}
        <DialogFooter className="block w-full">
          <PostCommentForm
            parentId={parentId}
            commenterName={commenterName}
            className="bg-sub py-4 px-8 rounded-b-lg"
            postId={postId}
            ref={inputRef}
            setParentId={setParentId}
            setCommenterName={setCommenterName}
            postSlug={slug}
          />
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
