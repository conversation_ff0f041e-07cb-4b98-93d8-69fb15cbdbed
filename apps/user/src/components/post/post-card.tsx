import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useRouter } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { Post } from "@vtuber/services/content";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Card, CardContent, CardHeader } from "@vtuber/ui/components/card";
import { ExpandableText } from "@vtuber/ui/components/expandable-text";
import { Media } from "@vtuber/ui/components/media";
import { Spinner } from "@vtuber/ui/components/spinner";
import useIntersectionObserver from "@vtuber/ui/hooks/use-intersection-observer";
import { useIsMobile } from "@vtuber/ui/hooks/use-mobile";
import { cn } from "@vtuber/ui/lib/utils";
import { format } from "date-fns";
import { Heart, MessageCircle } from "lucide-react";
import { useMemo, useState } from "react";
import { PostCommentsModal } from "../campaign/post-comments-modal";
import { LikePost } from "./like-post";
import { PostCardMemberOnlyOverlay } from "./post-card-member-only-overlay";
import { PostCardNotLoggedInOverlay } from "./post-card-not-logged-in-overlay";
import { PostMedia } from "./post-media";

interface Props {
  className?: string;
  post: Post;
  variant?: "default" | "gradient";
  isPlanActive: boolean;
}

export const PostCard = ({ className, post, variant, isPlanActive }: Props) => {
  const router = useRouter();
  const { session } = useAuth();
  const { isMobile } = useIsMobile();
  const [opened, setOpened] = useState(false);
  const { ref, isIntersecting } = useIntersectionObserver({
    freezeOnceVisible: true,
  });

  const formatedDate = (date: Date) =>
    useMemo(() => {
      return format(date, "yyyy/MM/dd HH:mm");
    }, [date]);

  const onOpen = () => {
    if (isMobile) {
      router.navigate({
        to: "/vtuber/post/$id",
        params: {
          id: post.slug,
        },
      });
      return;
    }
    setOpened(true);
  };

  return (
    <Card
      className={cn(
        "border-none shadow-none md:px-[39px] md:py-[42px] py-6 px-5 rounded-sm",
        variant === "gradient" ? "bg-gradient-3" : "bg-dark",
        className,
      )}
      ref={ref}>
      <CardHeader
        className={cn(
          "p-0 rounded-xl overflow-hidden",
          isIntersecting && "animate-zoom-in duration-700",
        )}>
        <PostCommentsModal
          slug={post.slug}
          postId={post.id}
          opened={opened}
          setOpened={setOpened}
        />
        <PostMedia
          post={post}
          onOpen={onOpen}
          isPlanActive={isPlanActive}>
          <AspectRatio ratio={574 / 309}>
            <Media
              canEnableFullScreen={false}
              style={{
                viewTransitionName: `post-${post.id}`,
              }}
              type={post?.mediaType}
              src={post.media || ""}
              alt={post.title}
              showControls={false}
              className="w-full h-full object-cover rounded-sm aspect-auto"
            />
          </AspectRatio>
        </PostMedia>
      </CardHeader>
      <CardContent className="p-0 pt-6 space-y-5 relative">
        <h3 className="sm:text-2xl text-lg font-bold">{post.title}</h3>
        <p className="sm:text-sm text-xs font-medium text-font">
          {formatedDate(timestampDate(post.createdAt!))}
        </p>
        {!session && post.membershipOnly && <PostCardNotLoggedInOverlay />}
        {session && post.membershipOnly && (
          <PostCardMemberOnlyOverlay
            isPlanActive={isPlanActive}
            vtuberId={post.vtuber?.id!}
          />
        )}

        <ExpandableText
          className="sm:text-base text-sm text-font block"
          wrapperClassName="space-y-3"
          length={250}
          text={post.shortDescription}
        />
        <div className="flex items-center gap-3">
          <LikePost
            userName={post.vtuber?.username}
            postId={post.id}
            hasLiked={post.hasLiked}
            className="p-0 hover:bg-transparent"
            postLikes={post.postLikes}>
            {({ postLikes, isLiked, isLoading }) => (
              <div className="flex items-center gap-x-3">
                {isLoading ? (
                  <Spinner className="size-[21px]" />
                ) : (
                  <Heart
                    className={cn(
                      "h-[21px] w-[22px] transition-all duration-200",
                      isLiked
                        ? "text-red-500 fill-red-500 scale-110"
                        : "text-font",
                    )}
                  />
                )}
                <small className="text-sm font-medium text-font">
                  {postLikes}
                </small>
              </div>
            )}
          </LikePost>

          <button
            onClick={onOpen}
            className="flex items-center gap-x-3">
            <MessageCircle className="scale-[-1] rotate-90 text-font h-[21px] w-[22px]" />
            <small className="text-sm font-medium text-font">
              {post.postComments}
            </small>
          </button>
        </div>
      </CardContent>
    </Card>
  );
};
