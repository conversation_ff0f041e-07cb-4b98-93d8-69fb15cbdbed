import { useNavigate } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import { Input } from "@vtuber/ui/components/input";
import { Label } from "@vtuber/ui/components/label";
import { cn } from "@vtuber/ui/lib/utils";
import { ArrowRight, Search } from "lucide-react";
import { useState } from "react";
interface Props {
  setShowSearch?: React.Dispatch<React.SetStateAction<boolean>>;
  className?: string;
  categories: string[];
  buttonWrapperClassName?: string;
  autoFocus?: boolean;
}

export const SearchBar = ({
  setShowSearch,
  className,
  categories,
  buttonWrapperClassName,
  autoFocus = false,
}: Props) => {
  const navigate = useNavigate();
  const { getText } = useLanguage();
  const [query, setQuery] = useState<string>();
  const disableButton = !query?.replaceAll(" ", "").length;
  return (
    <div className={cn("space-y-[29px]", className)}>
      <section className="space-y-[10px]">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            navigate({
              to: "/search",
              search: {
                query,
              },
            });
            setShowSearch?.(false);
          }}
          className="space-y-[10px]">
          <Label>{getText("search_by_keyword")}</Label>
          <Input
            value={query}
            onChange={(e) => {
              setQuery(e.target.value);
            }}
            autoFocus={autoFocus}
            rightIconClassName="right-0"
            rightIcon={
              <Button
                type="submit"
                className="h-full bg-[#9B95BA] size-[56px] hover:bg-purple01 rounded-l-none">
                <Search />
              </Button>
            }
            className="h-[56px] rounded-xs bg-[#26243A]"
            placeholder={getText("search_bar_placeholder")}
          />
        </form>
        <div className="flex items-center gap-[26px] text-xs font-medium text-[#DADADA] flex-wrap">
          {categories.map((c, i) => (
            <button
              onClick={() => {
                navigate({
                  to: "/search",
                  search: {
                    query: c,
                  },
                });
                setShowSearch?.(false);
              }}
              className="hover:text-tertiary"
              key={i}>
              {c}
            </button>
          ))}
        </div>
      </section>
      <section
        className={cn(
          "flex sm:flex-row flex-col items-center gap-x-[59px] sm:gap-y-0 gap-y-6 md:max-w-3xl mx-auto",
          buttonWrapperClassName,
        )}>
        <Button
          disabled={disableButton}
          onClick={() => {
            navigate({
              to: "/search",
              search: {
                query,
              },
            });
            setShowSearch?.(false);
          }}
          className="w-full h-[54px] bg-purple01 text-white font-bold hover:bg-transparent border-purple01 border [&>div]:justify-between [&>div]:w-full px-[22px] disabled:cursor-not-allowed">
          <p>{getText("find_vtuber")}</p>
          <ArrowRight />
        </Button>
        <Button
          disabled={disableButton}
          onClick={() => {
            navigate({
              to: "/search",
              search: {
                query,
              },
            });
            setShowSearch?.(false);
          }}
          className="w-full h-[54px] bg-[#1F8289] text-white font-bold hover:bg-transparent border border-[#1F8289] [&>div]:justify-between [&>div]:w-full px-[22px] disabled:cursor-not-allowed">
          <p>{getText("find_campaign")}</p>
          <ArrowRight />
        </Button>
      </section>
    </div>
  );
};

{
  /* <Button
  onClick={() => {
    navigate({
      to: "/search",
      search: {
        query: "vtuber",
      },
    });
    setShowSearch?.(false);
  }}
  className="w-full h-[54px] bg-[#1F8289] text-white font-bold hover:bg-white/5 px-[22px]">
  <p>クラウドファンディングを探す</p>
  <ArrowRight />
</Button>; */
}
