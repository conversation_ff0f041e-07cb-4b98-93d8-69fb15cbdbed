import { DisplayTag } from "@vtuber/ui/components/display-tag";

export const SearchNoResultsMessage = () => {
  return (
    <div className="space-y-[52px] lg:max-w-[1000px] mx-auto">
      <section className="space-y-6 text-center">
        <p className="text-[32px] font-medium text-[#D4594B]">
          0<span className="font-medium text-base text-font">件</span>
        </p>
        <h3 className="text-font text-[28px] font-bold">
          “あいうえお” に一致する情報は見つかりませんでした。
        </h3>
        <div className="font-medium text-[#D4594B]">
          <p>ご入力のキーワードに該当するVTuberは0人です。</p>
          <p>キーワードを変更して再度検索してください。</p>
        </div>
      </section>
      <section className="space-y-6">
        <DisplayTag
          type="underlined"
          text="trouble_searching"
        />
        <p>
          検索するキーワードでお困りでしたら、以下の関連キーワードをご利用ください。
        </p>
        <div className="flex items-center gap-2 flex-wrap">
          <button className="bg-sub text-font text-sm font-medium hover:text-tertiary rounded-xs px-4 py-[9px]">
            3Dモデル制作
          </button>
          <button className="bg-sub text-font text-sm font-medium hover:text-tertiary rounded-xs px-4 py-[9px]">
            衣装制作
          </button>
          <button className="bg-sub text-font text-sm font-medium hover:text-tertiary rounded-xs px-4 py-[9px]">
            3Dモデル制作
          </button>
          <button className="bg-sub text-font text-sm font-medium hover:text-tertiary rounded-xs px-4 py-[9px]">
            衣装制作
          </button>
        </div>
      </section>
      <section className="space-y-8">
        <DisplayTag
          type="underlined"
          text="search_tips"
        />
        <ul className="list-disc list-inside text-font space-y-3">
          <li>
            英数字は全角/半角/大文字/小文字の区別をせず、同じ検索結果となります。
          </li>
          <li>
            キーワード全てを含むページを探すには、キーワードの間にスペースを入れて検索してください。
          </li>
          <li>
            同じ意味で短く簡単なキーワードや、一般的な言葉に置き換えて検索してください。
          </li>
          <li>
            キーワードが正しく入力されているか、誤字がないか確認してください。
          </li>
        </ul>
      </section>
    </div>
  );
};
