import { Link, useLocation } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@vtuber/ui/components/dropdown-menu";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@vtuber/ui/components/hover-card";
import { Plus } from "lucide-react";
import { useState } from "react";
import { vtuberURL } from "~/data/constants";

export const UnauthenticatedUserOptions = () => {
  const { pathname } = useLocation();
  const [loginOpened, setLoginOpened] = useState(false);
  const [mobileLoggedIn, setMobileLoggedIn] = useState(false);
  const { getText } = useLanguage();
  return (
    <div>
      <div className="sm:flex hidden items-center gap-x-[22px]">
        <HoverCard openDelay={0}>
          <HoverCardTrigger asChild>
            <Button
              variant={"tertiary"}
              className="w-[166px] hover:bg-background border border-tertiary hover:text-tertiary group relative overflow-hidden"
              size={"xl"}>
              <div className="absolute inset-0 gap-[10px] flex items-center justify-center transition-transform duration-300 ease-in-out group-hover:-translate-y-full">
                <p className="font-bold">{getText("register_for_free")}</p>
                <Plus
                  className="size-5"
                  strokeWidth={2}
                />
              </div>

              <div className="absolute inset-0 flex gap-[10px] items-center justify-center transition-transform duration-300 ease-in-out translate-y-full group-hover:translate-y-0">
                <p className="font-bold">{getText("register_for_free")}</p>
                <Plus
                  className="size-5"
                  strokeWidth={2}
                />
              </div>
            </Button>
          </HoverCardTrigger>
          <HoverCardContent
            style={{
              filter: "drop-shadow(0px 0px 6px #fff)",
            }}
            align="center"
            className="bg-background border-0 shadow-none flex flex-col py-3 px-[14px] w-[166px]">
            <a
              href={vtuberURL + "register"}
              target="_blank"
              className="font-bold text-sm font-mplus py-3 hover:text-tertiary">
              {getText("register_as_vtuber")}
            </a>
            <Link
              to="/register"
              className="font-bold text-sm font-mplus py-3 hover:text-tertiary">
              {getText("sign_up_as_fan")}
            </Link>
          </HoverCardContent>
        </HoverCard>
        <DropdownMenu
          open={loginOpened}
          onOpenChange={setLoginOpened}>
          <DropdownMenuTrigger asChild>
            <Button
              variant={loginOpened ? "sub-outline" : "muted-outline"}
              className="font-bold relative overflow-hidden hover:bg-font hover:text-background"
              size={"xl"}>
              <div className="flex items-center gap-3 relative z-20">
                {getText("login").replace(" ", "")} <Plus className="!size-6" />
              </div>
              <div className="absolute inset-0 bg-gradient-3 xl:hidden block" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="start"
            className="rounded-none min-w-44">
            <DropdownMenuItem
              className="font-bold text-font hover:!bg-transparent hover:!text-tertiary py-3 cursor-pointer"
              asChild>
              <a
                href={vtuberURL + "login"}
                target="_blank">
                {getText("login_as_vtuber")}
              </a>
            </DropdownMenuItem>
            <DropdownMenuItem className="font-bold text-font hover:!bg-transparent hover:!text-tertiary py-3 cursor-pointer">
              <Link
                to="/login"
                search={{ redirect: pathname }}>
                {getText("login_page_subtitle")}
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="sm:hidden flex">
        <DropdownMenu
          open={mobileLoggedIn}
          onOpenChange={setMobileLoggedIn}>
          <DropdownMenuTrigger asChild>
            <Button
              variant={mobileLoggedIn ? "outline" : "tertiary"}
              className="font-bold rounded-full">
              {getText("login_register")}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="start"
            className="rounded-none min-w-44">
            <DropdownMenuItem
              className="font-bold text-font hover:!bg-transparent hover:!text-tertiary py-3 cursor-pointer"
              asChild>
              <a
                href={vtuberURL + "login"}
                target="_blank">
                {getText("login_as_vtuber")}
              </a>
            </DropdownMenuItem>
            <DropdownMenuItem className="font-bold text-font hover:!bg-transparent hover:!text-tertiary py-3 cursor-pointer">
              <Link
                to="/login"
                search={{ redirect: pathname }}>
                {getText("login_page_subtitle")}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="font-bold text-font hover:!bg-transparent hover:!text-tertiary py-3 cursor-pointer"
              asChild>
              <a
                href={vtuberURL + "register"}
                target="_blank">
                {getText("register_as_vtuber")}
              </a>
            </DropdownMenuItem>
            <DropdownMenuItem className="font-bold text-font hover:!bg-transparent hover:!text-tertiary py-3 cursor-pointer">
              <Link to="/register">{getText("sign_up_as_fan")}</Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};
